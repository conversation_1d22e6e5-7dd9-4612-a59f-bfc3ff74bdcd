# 快速参考指南

## 项目信息
- **项目类型**: 微信小程序 - 工作时间追踪和收入管理
- **架构模式**: 分层架构 (Pages → Services → Managers → Utils)
- **数据兼容性**: 新项目，无需考虑数据兼容性

## 目录结构速查

```
miniprogram/
├── core/                                  # 核心业务模块
│   ├── api/                               # API客户端
│   │   ├── base.js                        # 基础API客户端
│   │   ├── enhanced.js                    # 增强API客户端
│   │   ├── modules/                       # API模块（用户、签到、积分、商店等）
│   │   └── utils/                         # API工具（缓存、重试）
│   ├── managers/                          # 数据管理器
│   │   ├── data-manager.js                # 主数据管理器
│   │   ├── storage-manager.js             # 存储管理 (Deflate压缩)
│   │   ├── work-manager.js                # 工作履历管理
│   │   ├── settings-manager.js            # 设置管理
│   │   ├── time-tracking-manager.js       # 时间追踪管理
│   │   ├── user-manager.js                # 用户管理
│   │   ├── sync-manager.js                # 同步管理
│   │   ├── fishing-manager.js             # 摸鱼管理
│   │   └── holiday-manager.js             # 节假日管理
│   └── services/                          # 业务服务
│       ├── dashboard-service.js           # 仪表盘服务
│       ├── work-history-service.js        # 工作履历服务
│       ├── time-segment-service.js        # 时间段服务
│       ├── statistics-service.js          # 统计分析服务
│       └── data-import-export-service.js  # 数据导入导出服务
├── utils/                                 # 工具类
│   ├── simple-real-time-income.js         # 简化实时收入计算器
│   ├── validators/                        # 验证器
│   ├── formatters/                        # 格式化器
│   └── helpers/                           # 辅助工具
├── pages/                                 # 页面
│   └── index/                             # 主页面（自主导航栏 + 仪表盘切换器）
├── components/                            # 组件
│   ├── dashboard-switcher/                # 仪表盘切换器（共用组件）
│   ├── dashboard1/                        # 经典仪表盘（自主导航栏）
│   ├── dashboard2/                        # 现代仪表盘（自主导航栏）
│   ├── fishing-control/                   # 摸鱼控制组件
│   └── fishing-remark-editor/             # 摸鱼备注编辑器组件
└── tests/                                 # 测试
```

## 导航栏架构速查

### 架构原则
- **完全自主**：每个页面/组件自己实现导航栏
- **个性化定制**：每个仪表盘有独特的颜色和按钮
- **共享切换**：仪表盘切换器统一管理
- **事件驱动**：通过事件实现组件间通信

### 导航栏类型

| 页面/组件 | 主题色彩 | 按钮配置 | 特殊功能 |
|-----------|----------|----------|----------|
| Index 引导页 | 蓝色渐变 | 无按钮 | 只显示标题 |
| Index 加载页 | 紫色渐变 | 无按钮 | 只显示标题 |
| Dashboard1 | 蓝紫色渐变 | 左侧：切换+设置 | 独立设置模态框（含小数位数） |
| Dashboard2 | 橙黄色渐变 | 左侧：切换+设置 | 独立设置模态框（含小数位数） |

### 事件流程速查

```javascript
// 显示仪表盘切换器
Dashboard → triggerEvent('showDashboardSwitcher') → Index → showDashboardSwitcher: true

// 隐藏仪表盘切换器
Dashboard-switcher → triggerEvent('hide') → Index → showDashboardSwitcher: false

// 切换仪表盘
Dashboard-switcher → triggerEvent('dashboardChange') → Index → 切换逻辑
```

### 新增仪表盘快速模板

```javascript
// 1. 组件结构
components/dashboard-new/
├── index.js      # 包含状态栏高度获取
├── index.wxml    # 包含自主导航栏
├── index.wxss    # 包含导航栏样式
└── index.json

// 2. 必需的事件处理
onShowDashboardSwitcher: function() {
  this.triggerEvent('showDashboardSwitcher')
}

// 3. 必需的样式适配
.main-content {
  margin-top: 132rpx; /* 为导航栏留空间 */
  height: calc(100vh - 132rpx);
}

// 4. 模态框防误关闭
onModalContentTap: function() {
  // 阻止事件冒泡，防止关闭模态框
}
```

### 设置管理速查

```javascript
// 仪表盘独立设置
dashboard1: {
  showRealTimeIncome: true,
  showQuickActions: true,
  chartHeight: 120,
  showCurrentWork: true,
  incomeDecimalPlaces: 3  // 默认3位小数
}

dashboard2: {
  showCountdown: true,
  showAllStats: true,
  circularProgressSize: 400,
  showCurrentWork: true,
  incomeDecimalPlaces: 3  // 默认3位小数
}
```

## 数据结构速查

### 时间段数据结构（优化后）
```javascript
// 存储格式 - 只保留核心字段
const timeSegment = {
  id: 0,              // 简化ID（递增数字）
  start: 540,         // 开始时间（分钟数，540 = 09:00）
  end: 720,           // 结束时间（分钟数，720 = 12:00）
  type: "work",       // 时间段类型（work, rest, overtime）
  income: 150         // 收入金额
}

// 显示格式 - 动态计算字段
const displaySegment = {
  ...timeSegment,
  hourlyRate: 50,           // 动态计算：income / ((end - start) / 60)
  startTime: "09:00",       // 动态格式化：minutesToTimeDisplay(start)
  endTime: "12:00",         // 动态格式化：minutesToTimeDisplay(end)
  duration: "3小时",         // 动态计算：formatDuration(end - start)
  durationMinutes: 180,     // 动态计算：end - start
  typeText: "工作"          // 动态映射：getTypeText(type)
}
```

### 工作履历数据结构
```javascript
// 存储格式
const workData = {
  id: "1752916815108",           // 工作履历ID（时间戳）
  company: "新公司",             // 公司名称
  position: "开发工程师",        // 职位
  startDate: "2024-01-01",       // 入职日期（YYYY-MM-DD格式）
  probationEndDate: "2024-03-01", // 试用期结束日期
  formalSalary: 10000,           // 正式工资
  probationSalary: 8000,         // 试用期工资
  notes: "备注信息",             // 备注
  payDays: [                     // 发薪日设置（支持正数和负数）
    {
      day: 15,                   // 每月15号（正数：第几号）
      name: "月中发薪"           // 发薪日名称
    },
    {
      day: -1,                   // 每月倒数第1天（负数：倒数第几天）
      name: "月末发薪"
    },
    {
      day: -3,                   // 每月倒数第3天
      name: "月末前发薪"
    }
  ]
}

// 显示格式 - 动态计算字段
const displayWorkData = {
  ...workData,
  workDays: 120,                 // 动态计算：入职天数
  isInProbation: false,          // 动态计算：是否在试用期
  currentSalary: 10000,          // 动态计算：当前工资
  nextPayDay: {                  // 动态计算：下一个发薪日信息
    days: 5,                     // 距离天数
    payDayName: "月中发薪",      // 发薪日名称
    nextPayDate: "2025-07-24"    // 下一个发薪日期
  }
}
```

### 日期数据结构
```javascript
// 存储格式
const dayData = {
  date: "2025-07-19",       // 工作日期（YYYY-MM-DD格式）
  segments: [timeSegment],  // 时间段数组
  status: "active"          // 日期状态
}

// 显示格式 - 动态计算字段
const displayDayData = {
  ...dayData,
  dailyIncome: 400,         // 动态计算：sum(work segments income)
  totalWorkMinutes: 480,    // 动态计算：sum(work segments duration)
  totalWorkHours: 8,        // 动态计算：totalWorkMinutes / 60
  averageHourlyRate: 50,    // 动态计算：totalIncome / totalWorkHours
  segmentCount: 3,          // 动态计算：segments.length
  workSegmentCount: 2       // 动态计算：work segments count
}
```

### 时间格式转换速查
```javascript
// 分钟数 ↔ 时间显示
minutesToTimeDisplay(540)   // "09:00"
minutesToTimeDisplay(1560)  // "次日02:00"
timeStringToMinutes("09:00", false)  // 540
timeStringToMinutes("02:00", true)   // 1560

// 动态计算
calculateHourlyRate(segment)  // income / ((end - start) / 60)
formatDuration(180)          // "3小时"
formatTimeSegmentRange(540, 720)  // "09:00-12:00"
```

## 常用导入语句

### 核心模块
```javascript
// 主数据管理器
const dataManager = require('./core/managers/data-manager.js')

// 特定管理器
const { StorageManager } = require('./core/managers/storage-manager.js')
const { WorkManager } = require('./core/managers/work-manager.js')

// 业务服务
const { DashboardService } = require('./core/services/dashboard-service.js')
const { WorkHistoryService } = require('./core/services/work-history-service.js')

// 统一导入核心模块
const { dataManager, DashboardService } = require('./core/index.js')
```

### 工具类
```javascript
// 实时收入计算器
const SimpleRealTimeIncome = require('./utils/real-time-income.js')

// 验证器
const { DataValidator } = require('./utils/validators/data-validator.js')

// 格式化器
const { DataFormatter } = require('./utils/formatters/data-formatter.js')

// 辅助工具
const { DataCalculator } = require('./utils/helpers/data-calculator.js')
const { DataSecurity } = require('./utils/helpers/data-security.js')

// 统一导入工具类
const { DataValidator, DataFormatter } = require('./utils/index.js')
```

## 数据管理器API速查

### DataManager (主数据管理器)
```javascript
// 数据加载
await dataManager.ensureLoaded()
const userData = dataManager.getUserData()

// 工作履历管理
const workId = dataManager.addWork(workData)
dataManager.updateWork(workId, updateData)
dataManager.deleteWork(workId)
const work = dataManager.getWork(workId)
const currentWork = dataManager.getCurrentWork()

// 设置管理
const settings = dataManager.getSettings()
dataManager.updateSettings(newSettings)
dataManager.setCurrentDashboard('dashboard2')

// 时间追踪
const dayData = dataManager.getDayData(workId, date)
dataManager.saveDayData(workId, date, dayData)

// 数据保存（自动Deflate压缩）
dataManager.saveData() // 防抖保存
dataManager.saveData(true) // 立即保存

// 压缩信息会在控制台显示：
// 开始Deflate压缩，原始大小: 6885 字符
// Deflate压缩后大小: 4231 字符
// 压缩率: 38.54%
```

### WorkManager (工作履历管理器)
```javascript
// 创建和验证
const work = workManager.createWork(workData)
const validation = workManager.validateWorkData(workData)

// 业务计算
const workDays = workManager.calculateWorkDays(work)
const isInProbation = workManager.isInProbation(work)
const currentSalary = workManager.getCurrentSalary(work)

// 格式化显示
const displayInfo = workManager.formatWorkDisplay(work)
```

### SettingsManager (设置管理器)
```javascript
// 设置管理
const settings = settingsManager.initializeSettings()
const merged = settingsManager.mergeSettings(current, new)

// 仪表盘设置
const dashboardId = settingsManager.getCurrentDashboard(settings)
settingsManager.setCurrentDashboard(settings, 'dashboard2')

// 格式化
const formatted = settingsManager.formatCurrency(settings, 1234.56)
const masked = settingsManager.applyPrivacyMask(settings, 'field', 'value')
```

## 数据处理API速查

### WorkHistoryService (工作履历服务)
```javascript
// 发薪日管理
const payDays = workHistoryService.getPayDays(workId)
workHistoryService.updatePayDays(workId, payDaysArray)

// 发薪日计算
const payDayInfo = workHistoryService.getNextPayDayInfo(workId)
// 返回: {days: 5, payDayName: "月中发薪", nextPayDate: Date}

// 工作履历操作
const allWorks = workHistoryService.getAllWorkHistory()
const work = workHistoryService.getWorkHistory(workId)
const currentWorkId = workHistoryService.getCurrentWorkId()
workHistoryService.setCurrentWork(workId)

// 数据管理
const workId = workHistoryService.addWorkHistory(workData)
workHistoryService.updateWorkHistory(workId, updateData)
workHistoryService.deleteWorkHistory(workId)
```

### TimeSegmentService (时间段服务)
```javascript
// 数据转换
const displaySegment = timeSegmentService.convertSegmentForDisplay(segment)
const cleanedData = timeSegmentService.cleanDayDataForStorage(dayData)

// 时间段创建
const newSegment = timeSegmentService.createTimeSegment(540, 720, 'work', 150, 0)

// 日期计划设置
const result = timeSegmentService.setDaySchedule(date, timeInputs, totalIncome, workId)
```

### 时间工具函数
```javascript
// 时间转换
minutesToTimeDisplay(540)        // "09:00"
minutesToTimeDisplay(1560)       // "次日02:00"
timeStringToMinutes("22:00", false)  // 1320
timeStringToMinutes("02:00", true)   // 1560

// 时薪计算
calculateHourlyRate(segment)     // 动态计算时薪
formatDuration(240)              // "4小时"
formatTimeSegmentRange(1320, 1560)  // "22:00-次日02:00"

// 数据验证
validateTimeSegment(segment)     // 验证时间段数据
```

### 数据清理和优化
```javascript
// 清理时间段数据
function cleanSegmentData(segment, index = 0) {
  return {
    id: index,
    start: segment.start,
    end: segment.end,
    type: segment.type,
    income: segment.income || 0
  }
}

// 生成下一个ID
function generateNextId(segments) {
  if (!segments.length) return 0
  return Math.max(...segments.map(s => s.id || 0)) + 1
}

// 批量清理
function cleanSegmentsForStorage(segments) {
  return segments.map((segment, index) => cleanSegmentData(segment, index))
}
```

## 工具类API速查

### SimpleRealTimeIncome (实时收入计算器)
```javascript
// 创建实例
const realTimeIncome = new SimpleRealTimeIncome()

// 设置小数位数
realTimeIncome.setDecimalPlaces(3) // 0-3位小数

// 启动实时更新
realTimeIncome.start(
  (result) => {
    console.log('实时收入:', result.formattedIncome)
    console.log('是否在工作:', result.isWorking)
    console.log('时薪:', result.hourlyRate)
  },
  {
    workData: todayData,      // 今日工作数据
    currentWork: currentWork, // 当前工作履历
    baseService: baseService  // 基础服务
  }
)

// 停止实时更新
realTimeIncome.stop()

// 获取状态信息
const status = realTimeIncome.getStatus()
// { isRunning: true, decimalPlaces: 3, updateInterval: 22 }

// 手动计算收入
const result = realTimeIncome.calculateCurrentIncome(workData, currentWork, baseService)
// {
//   income: 125.456,
//   formattedIncome: "125.456",
//   isWorking: true,
//   hourlyRate: 150,
//   workedHours: 0.836,
//   currentSegmentIndex: 1
// }
```

#### 高频刷新特性
```javascript
// 刷新间隔自动计算示例
// 150元/小时 + 3位小数 → 22ms刷新 (45.5次/秒)
// 80元/小时 + 2位小数 → 427ms刷新 (2.3次/秒)
// 20元/小时 + 1位小数 → 1000ms刷新 (1.0次/秒)

// 动态调整触发条件
// 1. 用户修改小数位数
// 2. 进入新的时间段
// 3. 时薪发生变化
```

#### 多重时薪获取策略
```javascript
// 策略优先级：
// 1. currentWork.hourlyRate (直接时薪)
// 2. currentWork.monthlySalary / (22 * 8) (月薪计算)
// 3. workManager.getCurrentSalary() (数据管理器)
// 4. 50 (默认时薪)
```

### DataValidator (数据验证)
```javascript
// 基础验证
DataValidator.isValidDate('2024-01-01')
DataValidator.isPositiveNumber(100)
DataValidator.isNonEmptyString('text')

// 格式验证
DataValidator.isValidEmail('<EMAIL>')
DataValidator.isValidPhone('***********')
DataValidator.isValidIdCard('****************78')

// 密码验证
const result = DataValidator.validatePassword('password123', {
  minLength: 8,
  requireUppercase: true
})
```

### DataFormatter (数据格式化)
```javascript
// 货币格式化
DataFormatter.formatCurrency(1234.56, {
  symbol: '¥',
  decimals: 2
}) // ¥1,234.56

// 时间格式化
DataFormatter.formatDuration(125) // 2小时5分钟
DataFormatter.formatDate(new Date(), 'YYYY-MM-DD HH:mm')
DataFormatter.formatRelativeTime(date) // 今天/昨天/3天前

// 其他格式化
DataFormatter.formatPhone('***********') // 138 1234 5678
DataFormatter.formatFileSize(1024) // 1.00 KB
DataFormatter.formatPercentage(25, 100) // 25.0%
```

### DataCalculator (数据计算)
```javascript
// 日期计算
DataCalculator.daysBetween('2024-01-01', '2024-01-15') // 14
DataCalculator.workdaysBetween(startDate, endDate) // 排除周末

// 统计计算
DataCalculator.average([10, 20, 30]) // 20
DataCalculator.median([1, 2, 3, 4, 5]) // 3
DataCalculator.standardDeviation([1, 2, 3, 4, 5])

// 百分比和增长率
DataCalculator.percentage(25, 100) // 25
DataCalculator.growthRate(100, 120) // 20

// 其他计算
DataCalculator.calculateAge('1990-01-01') // 年龄
DataCalculator.calculateBMI(70, 1.75) // BMI指数
```

### DataSecurity (数据安全)
```javascript
// 数据脱敏
DataSecurity.maskPhone('***********') // 138****5678
DataSecurity.maskEmail('<EMAIL>') // t**<EMAIL>
DataSecurity.maskIdCard('****************78') // 123456********5678
DataSecurity.maskBankCard('****************') // 1234********3456

// 密码强度检查
const strength = DataSecurity.checkPasswordStrength('password123')
// { level: 2, description: '一般', score: 2, checks: {...} }

// 加密解密 (简单演示用)
const encrypted = DataSecurity.simpleEncrypt('text', 3)
const decrypted = DataSecurity.simpleDecrypt(encrypted, 3)

// Base64编码
const encoded = DataSecurity.base64Encode('text')
const decoded = DataSecurity.base64Decode(encoded)
```

## 页面开发模板

### 基础页面模板
```javascript
// pages/example/index.js
const { ExampleService } = require('../../core/services/example-service.js')

Page({
  data: {
    loading: false,
    error: null,
    pageData: null
  },

  onLoad(options) {
    this.service = new ExampleService()
    this.loadData()
  },

  async loadData() {
    this.setData({ loading: true, error: null })
    
    try {
      const result = await this.service.getData()
      if (result.success) {
        this.setData({ 
          pageData: result.data,
          loading: false 
        })
      } else {
        throw new Error(result.message)
      }
    } catch (error) {
      this.setData({ 
        error: error.message,
        loading: false 
      })
    }
  }
})
```

### 摸鱼备注编辑器API速查

#### FishingRemarkEditor 组件
```javascript
// 组件属性
properties: {
  show: { type: Boolean, value: false },           // 是否显示编辑器
  currentRemark: { type: String, value: '' }       // 当前备注内容
}

// 组件事件
this.triggerEvent('save', { remark: '新备注' })    // 保存备注事件
this.triggerEvent('cancel')                        // 取消编辑事件
this.triggerEvent('close')                         // 关闭编辑器事件

// 核心方法
loadQuickRemarks()                                 // 加载快捷备注
getQuickRemarksFromHistory(dataManager)           // 获取历史备注统计
createTestFishingData(dataManager)                // 创建测试数据
```

#### 使用示例
```javascript
// 在 fishing-control 中使用
<fishing-remark-editor
  show="{{showRemarkEditor}}"
  current-remark="{{fishingState.remark}}"
  bind:save="onRemarkEditorSave"
  bind:cancel="onRemarkEditorClose"
  bind:close="onRemarkEditorClose">
</fishing-remark-editor>

// 事件处理
onRemarkEditorSave(e) {
  const { remark } = e.detail
  // 更新本地状态
  this.setData({ 'fishingState.remark': remark })
}
```

#### 数据管理器API
```javascript
// 更新当前摸鱼备注
const result = dataManager.updateCurrentFishingRemark('新备注')
// 返回: { success: true, fishingState: {...} }

// 获取当前摸鱼状态
const fishingState = dataManager.getCurrentFishingState()
// 返回: { workId, date, startTime, remark, isActive, ... }
```

#### 快捷备注数据结构
```javascript
// 快捷备注项
{
  remark: '喝水休息',    // 备注内容
  count: 5              // 使用次数
}

// 快捷备注列表（按使用频次排序）
[
  { remark: '喝水休息', count: 5 },
  { remark: '上厕所', count: 3 },
  { remark: '看新闻', count: 2 }
]
```

### 组件开发模板
```javascript
// components/example/index.js
Component({
  properties: {
    data: { type: Object, value: {} }
  },

  data: {
    internalData: null
  },

  lifetimes: {
    attached() {
      this.processData(this.properties.data)
    }
  },

  methods: {
    processData(data) {
      this.setData({
        internalData: this.formatData(data)
      })
    },

    formatData(data) {
      return data
    },

    handleAction(e) {
      this.triggerEvent('action', {
        type: e.currentTarget.dataset.type,
        data: e.currentTarget.dataset.data
      })
    }
  }
})
```

## 错误处理模板

### 统一错误响应格式
```javascript
// 成功
{ success: true, data: result, message: '操作成功' }

// 失败
{ success: false, error: 'ERROR_CODE', message: '用户友好信息' }
```

### 错误处理示例
```javascript
// 管理器层
try {
  const result = manager.operation(data)
  return result
} catch (error) {
  console.error('[ERROR] 管理器操作失败:', error)
  throw error
}

// 服务层
try {
  const result = await manager.operation(data)
  return { success: true, data: result }
} catch (error) {
  console.error('[ERROR] 服务操作失败:', error)
  return { success: false, error: error.message }
}

// 页面层
const result = await service.operation(data)
if (!result.success) {
  wx.showToast({
    title: result.message || '操作失败',
    icon: 'error'
  })
  return
}
```

## 性能优化要点

### 缓存使用
```javascript
// 在管理器中使用缓存
if (this.isCacheValid() && this.cache.data) {
  return this.cache.data
}

const data = this.loadData()
this.cache = { data, timestamp: Date.now() }
return data
```

### 防抖保存
```javascript
// 防抖保存数据
saveData() {
  if (this.saveTimer) clearTimeout(this.saveTimer)
  this.saveTimer = setTimeout(() => this.performSave(), 1000)
}
```

### 模块化加载
```javascript
// 使用require加载模块
loadFeature() {
  const { Feature } = require('./feature.js')
  return new Feature()
}
```

## 微信小程序特殊注意事项

### 避免的语法
```javascript
// ❌ 扩展运算符
const obj = { ...oldObj, newProp: 'value' }

// ✅ Object.assign
const obj = Object.assign({}, oldObj, { newProp: 'value' })
```

### 样式单位
```css
/* ✅ 使用rpx */
.container { width: 750rpx; }

/* ❌ 避免px */
.container { width: 375px; }
```

### WXML表达式
```html
<!-- ❌ 复杂表达式 -->
<view>{{ items.filter(item => item.active).length }}</view>

<!-- ✅ 在JS中处理 -->
<view>{{ activeCount }}</view>
```

## 调试技巧

### 开发环境调试
```javascript
if (__DEV__) {
  console.group('调试信息')
  console.log('数据:', data)
  console.log('状态:', state)
  console.groupEnd()
}
```

### 性能监控
```javascript
const start = Date.now()
// 执行操作
console.log(`耗时: ${Date.now() - start}ms`)
```

## 实时收入系统开发模板

### 仪表盘组件集成
```javascript
// components/dashboard/index.js
const SimpleRealTimeIncome = require('../../utils/real-time-income.js')

Component({
  data: {
    currentIncome: '0.00',
    isWorking: false
  },

  lifetimes: {
    attached() {
      // 初始化实时收入计算器
      this.realTimeIncome = new SimpleRealTimeIncome()
      this.initializeRealTimeIncome()
    },

    detached() {
      // 清理资源
      if (this.realTimeIncome) {
        this.realTimeIncome.stop()
      }
    }
  },

  methods: {
    initializeRealTimeIncome() {
      // 设置小数位数
      const settings = this.getSettings()
      this.realTimeIncome.setDecimalPlaces(settings.decimalPlaces || 2)

      // 启动实时更新
      this.startRealTimeUpdate()
    },

    startRealTimeUpdate() {
      if (!this.data.hasWorkHistory || !this.data.currentWorkId) {
        return
      }

      const currentWork = this.data.currentWork
      const todayData = this.getTodayData()

      this.realTimeIncome.start(
        (result) => {
          this.setData({
            currentIncome: result.formattedIncome,
            isWorking: result.isWorking,
            hourlyRate: result.hourlyRate.toFixed(2)
          })
        },
        {
          workData: todayData,
          currentWork: currentWork,
          baseService: this.baseService
        }
      )
    },

    onSettingsChange(newSettings) {
      // 设置变化时重新配置
      if (this.realTimeIncome) {
        this.realTimeIncome.setDecimalPlaces(newSettings.decimalPlaces)
      }
    }
  }
})
```

### 页面级集成
```javascript
// pages/dashboard/index.js
Page({
  data: {
    realTimeIncomeEnabled: true
  },

  onLoad() {
    this.initializeRealTimeIncome()
  },

  onUnload() {
    this.cleanupRealTimeIncome()
  },

  onShow() {
    // 页面显示时恢复实时更新
    if (this.realTimeIncomeEnabled) {
      this.resumeRealTimeUpdate()
    }
  },

  onHide() {
    // 页面隐藏时暂停实时更新
    this.pauseRealTimeUpdate()
  },

  initializeRealTimeIncome() {
    this.selectComponent('#dashboard').initializeRealTimeIncome()
  },

  toggleRealTimeIncome() {
    this.setData({
      realTimeIncomeEnabled: !this.data.realTimeIncomeEnabled
    })

    if (this.data.realTimeIncomeEnabled) {
      this.resumeRealTimeUpdate()
    } else {
      this.pauseRealTimeUpdate()
    }
  }
})
```

### 调试和监控
```javascript
// 调试日志过滤
console.log = (function(originalLog) {
  return function(...args) {
    if (args[0] && (args[0].includes("[SimpleRealTime]") || args[0].includes("[RealTimeIncome]"))) {
      originalLog.apply(console, args);
    }
  };
})(console.log);

// 性能监控
class PerformanceMonitor {
  static monitorRealTimeIncome(realTimeIncome) {
    const originalStart = realTimeIncome.start.bind(realTimeIncome)

    realTimeIncome.start = function(callback, options) {
      const startTime = Date.now()

      const wrappedCallback = (result) => {
        const updateTime = Date.now()
        console.log(`[Performance] 实时收入更新耗时: ${updateTime - startTime}ms`)
        callback(result)
      }

      return originalStart(wrappedCallback, options)
    }
  }
}
```

## 最佳实践总结

### 1. 性能优化
- 根据用户需求选择合适的小数位数
- 在页面隐藏时暂停实时更新
- 及时清理定时器避免内存泄漏

### 2. 用户体验
- 提供实时收入开关选项
- 设置变化立即生效
- 异常情况优雅降级

### 3. 调试支持
- 使用统一的日志前缀
- 提供详细的状态信息
- 监控性能指标

### 4. 代码质量
- 模块化设计便于测试
- 清晰的API接口
- 完善的错误处理

## 数据压缩速查

### Deflate压缩特点
- **算法**: Deflate (LZ77 + Huffman编码)
- **范围**: 所有数据都进行压缩，无论大小
- **自动化**: 完全自动，开发者无需关心
- **兼容性**: 向后兼容未压缩数据

### 压缩日志示例
```
开始Deflate压缩，原始大小: 6885 字符
Deflate压缩后大小: 4231 字符
压缩率: 38.54%
数据保存成功
```

### 数据格式
```javascript
// 压缩数据格式
"~DEFLATE~[compressed_data]"  // 压缩数据
"[original_data]"             // 未压缩数据（兼容）
```

## 摸鱼备注编辑功能速查

### 功能特性
- **简化开始流程**: 一键开始摸鱼，无需预填备注
- **随时编辑**: 摸鱼过程中可随时修改备注
- **智能快捷**: 基于历史数据的备注快捷选择
- **友好显示**: 空备注显示为"日常摸鱼"

### 开发要点
- **组件化设计**: fishing-remark-editor 独立组件
- **数据驱动**: 基于历史数据的智能推荐
- **用户体验**: 一键选择，自动保存
- **状态同步**: 备注修改后UI实时更新

### 调试技巧
```javascript
// 查看快捷备注统计
console.log('[FishingRemarkEditor] 备注频次统计:', remarkFrequency)

// 查看测试数据创建
console.log('[FishingRemarkEditor] 创建测试数据:', testRemarks)

// 查看数据结构兼容性
console.log('[FishingRemarkEditor] 工作履历字段:', userData.works || userData.workHistory)
```

## 数据导入导出功能速查

### DataImportExportService (数据导入导出服务)
```javascript
// 创建和初始化服务
const { DataImportExportService } = require('./core/services/data-import-export-service.js')
const importExportService = new DataImportExportService()
importExportService.initialize(dataManager)

// 导出数据
const exportResult = importExportService.exportData()
if (exportResult.success) {
  console.log('导出成功:', exportResult.formattedSize)
  // exportResult.data 包含导出的JSON数据
}

// 验证导入数据格式
const validation = importExportService.validateImportData(jsonString)
if (validation.isValid) {
  console.log('数据格式正确:', validation.stats)
  // validation.stats 包含数据统计信息
} else {
  console.log('数据格式错误:', validation.error)
}

// 导入数据
const importResult = importExportService.importData(jsonString)
if (importResult.success) {
  console.log('导入成功:', importResult.stats)
} else {
  console.log('导入失败:', importResult.error)
}

// 获取数据统计
const statsResult = importExportService.getStatistics()
console.log('当前数据统计:', statsResult.stats)

// 创建备份
const backupResult = importExportService.createBackup('backup_name')
console.log('备份创建成功:', backupResult.backupName)
```

### 数据统计信息结构
```javascript
// 导入导出数据统计
{
  workHistoryCount: 2,        // 工作履历数量
  totalDaysWithData: 15,      // 有数据的天数
  timeSegmentCount: 45,       // 时间段总数
  fishingRecordCount: 8,      // 摸鱼记录总数
  settingsCount: 3            // 设置项数量
}
```

### 页面集成示例
```javascript
// 在设置页面中使用
Page({
  onLoad() {
    this.dataImportExportService = new DataImportExportService()
    this.dataImportExportService.initialize(this.dataManager)
  },

  // 导出数据
  onExportData() {
    const result = this.dataImportExportService.exportData()
    if (result.success) {
      wx.setClipboardData({
        data: result.data,
        success: () => {
          wx.showModal({
            title: '导出成功',
            content: `数据已复制到剪贴板\n数据大小：${result.formattedSize}`,
            showCancel: false
          })
        }
      })
    }
  },

  // 导入数据
  onImportData() {
    wx.getClipboardData({
      success: (res) => {
        const validation = this.dataImportExportService.validateImportData(res.data)
        if (validation.isValid) {
          // 显示导入确认信息
          const statsText = `工作履历：${validation.stats.workHistoryCount}个\n记录天数：${validation.stats.totalDaysWithData}天`
          wx.showModal({
            title: '确认导入',
            content: `检测到有效数据：\n${statsText}\n\n是否导入？`,
            success: (res) => {
              if (res.confirm) {
                this.performImport(res.data)
              }
            }
          })
        }
      }
    })
  }
})
```

### 错误处理模式
```javascript
// 统一的错误处理
function handleImportExportResult(result, successMessage) {
  if (result.success) {
    wx.showToast({
      title: successMessage,
      icon: 'success'
    })
    return true
  } else {
    wx.showModal({
      title: '操作失败',
      content: result.error || result.message,
      showCancel: false
    })
    return false
  }
}

// 使用示例
const exportResult = importExportService.exportData()
handleImportExportResult(exportResult, '导出成功')
```

这个快速参考指南涵盖了日常开发中最常用的API和模式，包括Deflate压缩系统、实时收入系统、摸鱼备注编辑功能和数据导入导出功能，可以帮助开发者快速上手和查阅。
