/**
 * 数据同步API模块
 * 封装数据同步相关的API调用
 */

import baseApiClient from '../base.js'
import enhancedApiClient from '../enhanced.js'

/**
 * 数据同步API类
 */
export class SyncApi {
  constructor() {
    this.baseClient = baseApiClient
    this.enhancedClient = enhancedApiClient
    
    // 缓存配置
    this.cacheConfig = {
      // 数据同步相关操作不缓存（涉及状态变更）
      getCloudDataInfo: {
        ttl: 0,
        ignoreKeys: []
      },
      downloadUserData: {
        ttl: 0,
        ignoreKeys: []
      },
      uploadUserData: {
        ttl: 0,
        ignoreKeys: []
      },
      clearCloudData: {
        ttl: 0,
        ignoreKeys: []
      }
    }
  }

  /**
   * 获取云端数据信息
   * @param {Object} options - 调用选项
   * @returns {Promise<Object>} 云端数据信息
   */
  async getCloudDataInfo(options = {}) {
    const finalOptions = {
      cache: false,
      showLoading: false,
      showError: false,
      ...options
    }

    return this.enhancedClient.callWithOptions('getCloudDataInfo', {}, finalOptions)
  }

  /**
   * 下载用户数据
   * @param {Object} options - 调用选项
   * @returns {Promise<Object>} 用户数据
   */
  async downloadUserData(options = {}) {
    const finalOptions = {
      cache: false,
      showLoading: false,
      showError: false,
      ...options
    }

    return this.enhancedClient.callWithOptions('downloadUserData', {}, finalOptions)
  }

  /**
   * 上传用户数据
   * @param {Object} params - 上传参数
   * @param {Object} options - 调用选项
   * @returns {Promise<Object>} 上传结果
   */
  async uploadUserData(params = {}, options = {}) {
    const finalOptions = {
      cache: false,
      showLoading: false,
      showError: false,
      retry: true,
      retryConfig: {
        maxRetries: 2,
        baseDelay: 1000
      },
      ...options
    }

    return this.enhancedClient.callWithOptions('uploadUserData', params, finalOptions)
  }

  /**
   * 清空云端数据
   * @param {Object} options - 调用选项
   * @returns {Promise<Object>} 清空结果
   */
  async clearCloudData(options = {}) {
    const finalOptions = {
      cache: false,
      showLoading: false,
      showError: false,
      ...options
    }

    return this.enhancedClient.callWithOptions('clearCloudData', {}, finalOptions)
  }

  /**
   * 获取数据同步API统计信息
   */
  getStats() {
    return {
      cacheConfig: this.cacheConfig,
      supportedApis: [
        'getCloudDataInfo',
        'downloadUserData',
        'uploadUserData',
        'clearCloudData'
      ]
    }
  }
}

// 创建单例实例
const syncApi = new SyncApi()

export default syncApi
