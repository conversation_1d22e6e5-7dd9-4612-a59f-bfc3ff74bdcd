/* 数据管理页面样式 */

.data-management-container {
  padding: 20rpx;
  background-color: #f8fafc;
  min-height: 100vh;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.page-title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 10rpx;
}

.page-subtitle {
  display: block;
  font-size: 28rpx;
  color: #6b7280;
}

/* 区块样式 */
.section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #374151;
  margin-bottom: 20rpx;
  padding-left: 10rpx;
}

/* 数据信息卡片 */
.data-info-card {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.data-info-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.data-info-icon {
  font-size: 40rpx;
  margin-right: 15rpx;
}

.data-info-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #1f2937;
}

.data-info-content {
  padding-left: 55rpx;
}

.data-info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.data-info-item:last-child {
  margin-bottom: 0;
}

.data-info-label {
  font-size: 28rpx;
  color: #6b7280;
}

.data-info-value {
  font-size: 28rpx;
  color: #1f2937;
  font-weight: 500;
}

.status-success {
  color: #10b981 !important;
}

.status-warning {
  color: #f59e0b !important;
}

/* 操作卡片 */
.operation-card {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.operation-header {
  display: flex;
  align-items: center;
  flex: 1;
}

.operation-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
}

.operation-info {
  flex: 1;
}

.operation-title {
  display: block;
  font-size: 30rpx;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 8rpx;
}

.operation-desc {
  display: block;
  font-size: 24rpx;
  color: #6b7280;
  line-height: 1.4;
}

.operation-btn {
  padding: 16rpx 32rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  min-width: 160rpx;
}

.operation-btn.primary {
  background-color: #3b82f6;
  color: white;
}

.operation-btn.primary:disabled {
  background-color: #9ca3af;
  color: #d1d5db;
}

.operation-btn.danger {
  background-color: #ef4444;
  color: white;
}

.operation-btn.danger:active {
  background-color: #dc2626;
}

.operation-btn.primary:active {
  background-color: #2563eb;
}

/* 警告卡片 */
.warning-card {
  background: #fef3c7;
  border: 2rpx solid #f59e0b;
  border-radius: 16rpx;
  padding: 30rpx;
}

.warning-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.warning-icon {
  font-size: 40rpx;
  margin-right: 15rpx;
}

.warning-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #92400e;
}

.warning-content {
  padding-left: 55rpx;
}

.warning-text {
  display: block;
  font-size: 26rpx;
  color: #92400e;
  line-height: 1.5;
  margin-bottom: 10rpx;
}

.warning-text:last-child {
  margin-bottom: 0;
}

/* 刷新按钮 */
.refresh-section {
  text-align: center;
  margin-top: 40rpx;
}

.refresh-btn {
  background: white;
  border: 2rpx solid #e5e7eb;
  border-radius: 50rpx;
  padding: 20rpx 40rpx;
  display: inline-flex;
  align-items: center;
  font-size: 28rpx;
  color: #6b7280;
}

.refresh-btn:active {
  background-color: #f9fafb;
}

.refresh-icon {
  margin-right: 10rpx;
  font-size: 32rpx;
}

.refresh-text {
  font-weight: 500;
}
