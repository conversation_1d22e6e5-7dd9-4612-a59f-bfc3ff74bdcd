// 数据管理页面
import { api } from '../../core/api/index.js'
import { formatDateTime, formatRelativeTime } from '../../utils/time-utils.js'

Page({
  data: {
    // 本地数据信息
    localDataInfo: {
      lastModified: '',
      size: ''
    },

    // 云端数据信息
    cloudDataInfo: {
      lastModified: '',
      hasData: false
    },

    // 同步状态
    syncStatus: {
      isInitialized: false,
      isSyncing: false,
      lastSyncTime: null,
      lastSyncTimeText: '从未同步',
      hasPermission: false
    },

    // 加载状态
    loading: false
  },

  /**
   * 页面加载时
   */
  onLoad() {
    console.log('[DataManagement] 页面加载')
    
    // 获取全局管理器
    this.dataManager = getApp().getDataManager()
    this.syncManager = getApp().getSyncManager()
    
    // 加载数据状态
    this.loadDataStatus()
  },

  /**
   * 页面显示时
   */
  onShow() {
    console.log('[DataManagement] 页面显示')
    
    // 刷新数据状态
    this.loadDataStatus()
  },

  /**
   * 加载数据状态
   */
  async loadDataStatus() {
    try {
      this.setData({ loading: true })
      
      // 并行加载本地和云端数据信息
      await Promise.all([
        this.loadLocalDataInfo(),
        this.loadCloudDataInfo(),
        this.loadSyncStatus()
      ])
      
    } catch (error) {
      console.error('[DataManagement] 加载数据状态失败:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  /**
   * 加载本地数据信息
   */
  loadLocalDataInfo() {
    try {
      const userData = this.dataManager.getUserData()
      
      // 计算数据大小（简单估算）
      const dataSize = this.calculateDataSize(userData)
      
      // 格式化最后修改时间
      const lastModified = userData.lastModified 
        ? formatDateTime(new Date(userData.lastModified))
        : '暂无数据'
      
      this.setData({
        'localDataInfo.lastModified': lastModified,
        'localDataInfo.size': dataSize
      })
      
      console.log('[DataManagement] 本地数据信息加载完成')
    } catch (error) {
      console.error('[DataManagement] 加载本地数据信息失败:', error)
      this.setData({
        'localDataInfo.lastModified': '加载失败',
        'localDataInfo.size': '未知'
      })
    }
  },

  /**
   * 加载云端数据信息
   */
  async loadCloudDataInfo() {
    try {
      const result = await api.sync.getCloudDataInfo()
      
      if (result.success) {
        const data = result.data
        const lastModified = data.lastModified 
          ? formatDateTime(new Date(data.lastModified))
          : '暂无数据'
        
        this.setData({
          'cloudDataInfo.lastModified': lastModified,
          'cloudDataInfo.hasData': data.hasData || false
        })
        
        console.log('[DataManagement] 云端数据信息加载完成')
      } else {
        throw new Error(result.message || '获取云端数据信息失败')
      }
    } catch (error) {
      console.error('[DataManagement] 加载云端数据信息失败:', error)
      this.setData({
        'cloudDataInfo.lastModified': '加载失败',
        'cloudDataInfo.hasData': false
      })
    }
  },

  /**
   * 加载同步状态
   */
  loadSyncStatus() {
    try {
      if (!this.syncManager) {
        console.warn('[DataManagement] 同步管理器未初始化')
        return
      }

      const syncStatus = this.syncManager.getSyncStatus()

      this.setData({
        'syncStatus.isInitialized': syncStatus.isInitialized,
        'syncStatus.isSyncing': syncStatus.isSyncing,
        'syncStatus.lastSyncTime': syncStatus.lastSyncTime,
        'syncStatus.lastSyncTimeText': syncStatus.lastSyncTimeText,
        'syncStatus.hasPermission': syncStatus.hasPermission
      })

      console.log('[DataManagement] 同步状态加载完成')
    } catch (error) {
      console.error('[DataManagement] 加载同步状态失败:', error)
      
      // 设置默认状态
      this.setData({
        'syncStatus.isInitialized': false,
        'syncStatus.isSyncing': false,
        'syncStatus.lastSyncTime': null,
        'syncStatus.lastSyncTimeText': '未同步',
        'syncStatus.hasPermission': false
      })
    }
  },

  /**
   * 计算数据大小（简单估算）
   */
  calculateDataSize(data) {
    try {
      const jsonString = JSON.stringify(data)
      const sizeInBytes = jsonString.length * 2 // 简单估算，每个字符约2字节

      // 输出到控制台
      console.log('[DataManagement] 数据大小估算:', sizeInBytes, '字节')

      if (sizeInBytes < 1024) {
        return `${sizeInBytes} B`
      } else if (sizeInBytes < 1024 * 1024) {
        return `${(sizeInBytes / 1024).toFixed(1)} KB`
      } else {
        return `${(sizeInBytes / (1024 * 1024)).toFixed(1)} MB`
      }
    } catch (error) {
      console.error('[DataManagement] 计算数据大小失败:', error)
      return '未知'
    }
  },

  /**
   * 清空本地数据
   */
  onClearLocalData() {
    wx.showModal({
      title: '确认清空本地数据',
      content: '此操作将清空所有本地数据，应用会自动重启。\n\n如果云端有数据，重启后可能会重新加载云端数据。\n\n确定要继续吗？',
      confirmText: '确定清空',
      confirmColor: '#ef4444',
      success: (res) => {
        if (res.confirm) {
          this.performClearLocalData()
        }
      }
    })
  },

  /**
   * 执行清空本地数据
   */
  async performClearLocalData() {
    try {
      wx.showLoading({ title: '清空中...' })
      
      // 清空本地数据
      this.dataManager.clearAllData()
      
      wx.hideLoading()
      
      // 显示成功提示
      wx.showModal({
        title: '清空成功',
        content: '本地数据已清空，应用即将重启。',
        showCancel: false,
        success: () => {
          // 重启应用
          this.restartApp()
        }
      })
      
    } catch (error) {
      wx.hideLoading()
      console.error('[DataManagement] 清空本地数据失败:', error)
      wx.showToast({
        title: '清空失败',
        icon: 'none'
      })
    }
  },

  /**
   * 清空云端数据
   */
  onClearCloudData() {
    wx.showModal({
      title: '确认清空云端数据',
      content: '此操作将删除云端存储的所有数据，且无法恢复。\n\n确定要继续吗？',
      confirmText: '确定清空',
      confirmColor: '#ef4444',
      success: (res) => {
        if (res.confirm) {
          this.performClearCloudData()
        }
      }
    })
  },

  /**
   * 执行清空云端数据
   */
  async performClearCloudData() {
    try {
      wx.showLoading({ title: '清空中...' })
      
      // 调用清空云端数据API
      const result = await api.sync.clearCloudData()
      
      wx.hideLoading()
      
      if (result.success) {
        wx.showToast({
          title: '云端数据已清空',
          icon: 'success'
        })
        
        // 刷新数据状态
        this.loadDataStatus()
      } else {
        throw new Error(result.message || '清空云端数据失败')
      }
      
    } catch (error) {
      wx.hideLoading()
      console.error('[DataManagement] 清空云端数据失败:', error)
      wx.showToast({
        title: error.message || '清空失败',
        icon: 'none'
      })
    }
  },

  /**
   * 手动同步
   */
  async onManualSync() {
    if (this.data.syncStatus.isSyncing) {
      wx.showToast({
        title: '同步进行中...',
        icon: 'loading'
      })
      return
    }

    try {
      wx.showLoading({ title: '同步中...' })
      
      await this.syncManager.manualSync()
      
      wx.hideLoading()
      
      // 刷新数据状态
      this.loadDataStatus()
      
    } catch (error) {
      wx.hideLoading()
      console.error('[DataManagement] 手动同步失败:', error)
      wx.showToast({
        title: '同步失败',
        icon: 'none'
      })
    }
  },

  /**
   * 刷新数据状态
   */
  onRefreshData() {
    this.loadDataStatus()
  },

  /**
   * 重启应用
   */
  restartApp() {
    try {
      // 微信小程序重启应用的方法
      wx.reLaunch({
        url: '/pages/index/index'
      })
    } catch (error) {
      console.error('[DataManagement] 重启应用失败:', error)
      wx.showToast({
        title: '请手动重启应用',
        icon: 'none'
      })
    }
  }
})
