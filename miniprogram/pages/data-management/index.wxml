<!-- 数据管理页面 -->
<view class="data-management-container">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">数据管理</text>
    <text class="page-subtitle">管理您的本地和云端数据</text>
  </view>

  <!-- 数据状态信息 -->
  <view class="section">
    <view class="section-title">数据状态</view>
    
    <!-- 本地数据信息 -->
    <view class="data-info-card">
      <view class="data-info-header">
        <view class="data-info-icon">📱</view>
        <view class="data-info-title">本地数据</view>
      </view>
      <view class="data-info-content">
        <view class="data-info-item">
          <text class="data-info-label">最后更新时间：</text>
          <text class="data-info-value">{{localDataInfo.lastModified || '暂无数据'}}</text>
        </view>
        <view class="data-info-item">
          <text class="data-info-label">数据大小：</text>
          <text class="data-info-value">{{localDataInfo.size || '0 KB'}}</text>
        </view>
      </view>
    </view>

    <!-- 云端数据信息 -->
    <view class="data-info-card">
      <view class="data-info-header">
        <view class="data-info-icon">☁️</view>
        <view class="data-info-title">云端数据</view>
      </view>
      <view class="data-info-content">
        <view class="data-info-item">
          <text class="data-info-label">最后更新时间：</text>
          <text class="data-info-value">{{cloudDataInfo.lastModified || '暂无数据'}}</text>
        </view>
        <view class="data-info-item">
          <text class="data-info-label">同步状态：</text>
          <text class="data-info-value {{syncStatus.isInitialized ? 'status-success' : 'status-warning'}}">
            {{syncStatus.isInitialized ? '已同步' : '未同步'}}
          </text>
        </view>
      </view>
    </view>
  </view>

  <!-- 数据操作 -->
  <view class="section">
    <view class="section-title">数据操作</view>
    
    <!-- 清空本地数据 -->
    <view class="operation-card">
      <view class="operation-header">
        <view class="operation-icon">🗑️</view>
        <view class="operation-info">
          <text class="operation-title">清空本地数据</text>
          <text class="operation-desc">清空所有本地存储的数据，应用将重启</text>
        </view>
      </view>
      <button class="operation-btn danger" bindtap="onClearLocalData">清空本地数据</button>
    </view>

    <!-- 清空云端数据 -->
    <view class="operation-card">
      <view class="operation-header">
        <view class="operation-icon">☁️</view>
        <view class="operation-info">
          <text class="operation-title">清空云端数据</text>
          <text class="operation-desc">删除云端存储的所有数据，不可恢复</text>
        </view>
      </view>
      <button class="operation-btn danger" bindtap="onClearCloudData">清空云端数据</button>
    </view>

    <!-- 手动同步 -->
    <view class="operation-card">
      <view class="operation-header">
        <view class="operation-icon">🔄</view>
        <view class="operation-info">
          <text class="operation-title">手动同步</text>
          <text class="operation-desc">立即同步本地和云端数据</text>
        </view>
      </view>
      <button class="operation-btn primary" bindtap="onManualSync" disabled="{{syncStatus.isSyncing}}">
        {{syncStatus.isSyncing ? '同步中...' : '立即同步'}}
      </button>
    </view>
  </view>

  <!-- 重要提示 -->
  <view class="section">
    <view class="warning-card">
      <view class="warning-header">
        <view class="warning-icon">⚠️</view>
        <text class="warning-title">重要提示</text>
      </view>
      <view class="warning-content">
        <text class="warning-text">• 清空本地数据后应用会自动重启</text>
        <text class="warning-text">• 如果云端有数据，重启后可能会重新加载云端数据</text>
        <text class="warning-text">• 建议先清空云端数据，再清空本地数据</text>
        <text class="warning-text">• 数据清空后无法恢复，请谨慎操作</text>
      </view>
    </view>
  </view>

  <!-- 刷新按钮 -->
  <view class="refresh-section">
    <button class="refresh-btn" bindtap="onRefreshData">
      <text class="refresh-icon">🔄</text>
      <text class="refresh-text">刷新数据状态</text>
    </button>
  </view>
</view>
