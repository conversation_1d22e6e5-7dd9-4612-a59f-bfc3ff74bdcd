/**
 * 数据同步相关API
 */

const userDataDB = require('../db/user-data')
const { getCurrentUser } = require('../utils/auth')
const { success, error, wrapAsync, validateRequired } = require('../utils/response')

/**
 * 获取云端数据信息
 */
exports.getCloudDataInfo = wrapAsync(async (params = {}) => {
  // 获取当前用户
  const userResult = await getCurrentUser()
  if (!userResult.success) {
    return userResult
  }
  const user = userResult.data

  // 获取用户数据信息
  const dataResult = await userDataDB.getUserData(user._id)
  if (!dataResult.success) {
    return dataResult
  }

  // 如果没有数据，返回基本信息
  if (!dataResult.hasData || !dataResult.data) {
    return success({
      hasData: false,
      timestamp: null,
      lastModified: null
    }, '获取云端数据信息成功')
  }

  return success({
    hasData: dataResult.hasData,
    timestamp: dataResult.timestamp || null,
    lastModified: dataResult.lastModified || null
  }, '获取云端数据信息成功')
})

/**
 * 下载用户数据
 */
exports.downloadUserData = wrapAsync(async (params = {}) => {
  // 获取当前用户
  const userResult = await getCurrentUser()
  if (!userResult.success) {
    return userResult
  }

  const user = userResult.data

  // 获取用户数据
  const dataResult = await userDataDB.getUserData(user._id)

  if (!dataResult.success) {
    return dataResult
  }

  if (!dataResult.hasData) {
    return success(null, '暂无云端数据')
  }

  return success(dataResult.data, '下载用户数据成功')
})

/**
 * 上传用户数据
 */
exports.uploadUserData = wrapAsync(async (params = {}) => {
  // 获取当前用户
  const userResult = await getCurrentUser()
  if (!userResult.success) {
    return userResult
  }

  const user = userResult.data

  // 保存用户数据
  const saveResult = await userDataDB.saveUserData(user._id, params.data)

  if (!saveResult.success) {
    return saveResult
  }

  return success(null, '上传用户数据成功')
})

/**
 * 获取历史数据列表（用于历史数据页面）
 */
exports.getHistoryDataList = wrapAsync(async (params = {}) => {
  // 获取当前用户
  const userResult = await getCurrentUser()
  if (!userResult.success) {
    return userResult
  }

  const user = userResult.data

  // 获取历史数据列表
  const historyResult = await userDataDB.getUserDataHistory(user._id, params)

  if (!historyResult.success) {
    return historyResult
  }

  return success(historyResult.data, '获取历史数据列表成功')
})

/**
 * 获取指定的历史数据
 */
exports.getHistoryData = wrapAsync(async (params = {}) => {
  validateRequired(params, ['dataId'])

  // 获取当前用户
  const userResult = await getCurrentUser()
  if (!userResult.success) {
    return userResult
  }

  const user = userResult.data

  // 获取历史数据详情
  const dataResult = await userDataDB.getHistoryDataById(params.dataId, user._id)

  if (!dataResult.success) {
    return dataResult
  }

  return success(dataResult.data, '获取历史数据成功')
})

/**
 * 清空云端数据
 */
exports.clearCloudData = wrapAsync(async (params = {}) => {
  // 获取当前用户
  const userResult = await getCurrentUser()
  if (!userResult.success) {
    return userResult
  }

  const user = userResult.data

  // 删除用户的云端数据
  const deleteResult = await userDataDB.deleteUserData(user._id)

  if (!deleteResult.success) {
    return deleteResult
  }

  console.log(`用户 ${user._id} 的云端数据已清空`)
  return success(null, '云端数据清空成功')
})